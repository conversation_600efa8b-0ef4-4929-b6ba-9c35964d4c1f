# Dashboard Performance Optimizations

"""
Các tối ưu hóa hiệu suất cho Dashboard API:

1. Database Indexes cần thiết:
   - User.created_time (index)
   - ChatMessage.created_at + type (composite index)
   - RequestLog.timestamp + feature_name (composite index)
   - RequestLog.user_id + timestamp (composite index)
   - OrganizationMember.organization_id (index)
   - Conversation.created_at (index)

2. Caching Strategy:
   - Cache dashboard data trong 5 phút
   - Cache key dựa trên user_id, organization_id, date range, config
   - Sử dụng Redis cache backend

3. Query Optimizations:
   - Sử dụng aggregation thay vì multiple queries
   - Batch queries với Q objects
   - select_related/prefetch_related cho foreign keys
   - Tránh N+1 queries

4. Code Optimizations:
   - Tách logic thành helper methods
   - Giảm code duplication
   - Sử dụng single responsibility principle

Để áp dụng các database indexes, chạy:
python manage.py makemigrations dashboard
python manage.py migrate
"""

# Database Migration để tạo indexes
DASHBOARD_INDEXES_MIGRATION = """
# Generated migration for dashboard indexes

from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('cls_backend', '0001_initial'),
        ('chatbot', '0001_initial'),
        ('request_log', '0001_initial'),
        ('organization', '0001_initial'),
    ]

    operations = [
        # Index cho User.created_time
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_user_created_time ON cls_backend_user(created_time);",
            reverse_sql="DROP INDEX IF EXISTS idx_user_created_time;"
        ),
        
        # Composite index cho ChatMessage
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_chatmessage_created_type ON chatbot_chatmessage(created_at, type);",
            reverse_sql="DROP INDEX IF EXISTS idx_chatmessage_created_type;"
        ),
        
        # Composite index cho RequestLog
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_requestlog_timestamp_feature ON request_log_requestlog(timestamp, feature_name);",
            reverse_sql="DROP INDEX IF EXISTS idx_requestlog_timestamp_feature;"
        ),
        
        # Index cho RequestLog user_id + timestamp
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_requestlog_user_timestamp ON request_log_requestlog(user_id, timestamp);",
            reverse_sql="DROP INDEX IF EXISTS idx_requestlog_user_timestamp;"
        ),
        
        # Index cho OrganizationMember
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_orgmember_org_id ON organization_organizationmember(organization_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_orgmember_org_id;"
        ),
        
        # Index cho Conversation.created_at
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_conversation_created ON chatbot_conversation(created_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_conversation_created;"
        ),
    ]
"""

# Cache configuration cho settings.py
CACHE_SETTINGS = """
# Thêm vào settings.py

CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
        'KEY_PREFIX': 'dashboard',
        'TIMEOUT': 300,  # 5 minutes
    }
}

# Hoặc nếu không có Redis, sử dụng database cache:
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
        'LOCATION': 'dashboard_cache_table',
        'TIMEOUT': 300,
    }
}
"""

# Performance monitoring
PERFORMANCE_MONITORING = """
# Thêm middleware để monitor performance

import time
import logging
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger('dashboard.performance')

class DashboardPerformanceMiddleware(MiddlewareMixin):
    def process_request(self, request):
        if request.path.startswith('/api/dashboard/'):
            request.start_time = time.time()
    
    def process_response(self, request, response):
        if hasattr(request, 'start_time') and request.path.startswith('/api/dashboard/'):
            duration = time.time() - request.start_time
            logger.info(f"Dashboard API {request.path} took {duration:.2f}s")
            
            # Alert nếu quá chậm
            if duration > 2.0:
                logger.warning(f"Slow dashboard query: {request.path} took {duration:.2f}s")
        
        return response
"""

# Celery task để pre-warm cache
CELERY_CACHE_WARMING = """
# tasks.py - Celery task để pre-warm cache

from celery import shared_task
from django.core.cache import cache
from django.contrib.auth import get_user_model
from datetime import datetime, timedelta
from dashboard.views import DashboardAPIView

User = get_user_model()

@shared_task
def warm_dashboard_cache():
    '''Pre-warm dashboard cache cho các users thường xuyên truy cập'''
    
    # Lấy danh sách users active trong 7 ngày qua
    week_ago = datetime.now() - timedelta(days=7)
    active_users = User.objects.filter(
        last_login__gte=week_ago
    ).values_list('id', flat=True)
    
    dashboard_view = DashboardAPIView()
    
    for user_id in active_users:
        try:
            user = User.objects.get(id=user_id)
            
            # Pre-warm cache cho các khoảng thời gian phổ biến
            date_ranges = [
                (datetime.now() - timedelta(days=7), datetime.now()),  # 7 ngày
                (datetime.now() - timedelta(days=30), datetime.now()), # 30 ngày
            ]
            
            for from_date, to_date in date_ranges:
                cache_key = f"dashboard_{user_id}_None_{from_date.isoformat()}_{to_date.isoformat()}_day"
                
                if not cache.get(cache_key):
                    # Tính toán và cache data
                    data = dashboard_view._get_optimized_dashboard_data(
                        user, None, from_date, to_date, 
                        from_date - (to_date - from_date), from_date, 'day'
                    )
                    cache.set(cache_key, data, 300)
                    
        except Exception as e:
            logger.error(f"Error warming cache for user {user_id}: {e}")

# Chạy task này mỗi 4 phút
from celery.schedules import crontab

CELERY_BEAT_SCHEDULE = {
    'warm-dashboard-cache': {
        'task': 'dashboard.tasks.warm_dashboard_cache',
        'schedule': crontab(minute='*/4'),
    },
}
"""

# Database connection pooling
DB_POOLING = """
# settings.py - Database connection pooling

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'your_db_name',
        'USER': 'your_db_user',
        'PASSWORD': 'your_db_password',
        'HOST': 'localhost',
        'PORT': '5432',
        'OPTIONS': {
            'MAX_CONNS': 20,
            'MIN_CONNS': 5,
        },
        'CONN_MAX_AGE': 600,  # 10 minutes
    }
}

# Hoặc sử dụng django-db-pool
DATABASES = {
    'default': {
        'ENGINE': 'dj_db_conn_pool.backends.postgresql',
        'NAME': 'your_db_name',
        'USER': 'your_db_user',
        'PASSWORD': 'your_db_password',
        'HOST': 'localhost',
        'PORT': '5432',
        'POOL_OPTIONS': {
            'POOL_SIZE': 10,
            'MAX_OVERFLOW': 10,
            'RECYCLE': 24 * 60 * 60,  # 24 hours
        }
    }
}
"""
