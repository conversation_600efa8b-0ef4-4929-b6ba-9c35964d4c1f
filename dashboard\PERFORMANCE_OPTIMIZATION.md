# Dashboard API Performance Optimization

## Tổng quan các tối ưu hóa đã thực hiện

### 1. Code Optimizations

#### Trước khi tối ưu:
- Nhiều queries riêng lẻ cho từng metric
- Code lặp lại cho các trường hợp khác nhau
- Không có caching
- N+1 queries trong vòng lặp organizations
- Logic phức tạp trong một method duy nhất

#### Sau khi tối ưu:
- ✅ Sử dụng Django aggregation để giảm số queries
- ✅ Tách logic thành các helper methods
- ✅ Thêm caching với timeout 5 phút
- ✅ Batch queries với Q objects
- ✅ Tối ưu organizations data với single query + annotation

### 2. Database Indexes

Đã thêm các indexes quan trọng:

```sql
-- User created_time index
CREATE INDEX idx_user_created_time ON cls_backend_user(created_time);

-- ChatMessage composite index
CREATE INDEX idx_chatmessage_created_type ON chatbot_chatmessage(created_at, type);

-- RequestLog composite indexes
CREATE INDEX idx_requestlog_timestamp_feature ON request_log_requestlog(timestamp, feature_name);
CREATE INDEX idx_requestlog_user_timestamp ON request_log_requestlog(user_id, timestamp);

-- Organization indexes
CREATE INDEX idx_orgmember_org_id ON organization_organizationmember(organization_id);

-- Conversation index
CREATE INDEX idx_conversation_created ON chatbot_conversation(created_at);
```

### 3. Caching Strategy

- **Cache Key**: `dashboard_{user_id}_{organization_id}_{from_date}_{to_date}_{config}`
- **Timeout**: 5 phút (300 seconds)
- **Backend**: Redis (khuyến nghị) hoặc Database cache

### 4. Performance Improvements

#### Ước tính cải thiện hiệu suất:

| Metric | Trước | Sau | Cải thiện |
|--------|-------|-----|-----------|
| Số queries | 15-25 | 3-5 | 70-80% |
| Response time | 2-5s | 0.2-0.8s | 80-90% |
| Database load | Cao | Thấp | 70% |
| Memory usage | Cao | Thấp | 50% |

## Cách áp dụng

### Bước 1: Chạy migrations để tạo indexes

```bash
python manage.py makemigrations dashboard
python manage.py migrate
```

### Bước 2: Cấu hình cache

Thêm vào `settings.py`:

```python
# Redis cache (khuyến nghị)
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
        'KEY_PREFIX': 'dashboard',
        'TIMEOUT': 300,
    }
}

# Hoặc database cache
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
        'LOCATION': 'dashboard_cache_table',
        'TIMEOUT': 300,
    }
}
```

Nếu dùng database cache, tạo cache table:
```bash
python manage.py createcachetable
```

### Bước 3: Cài đặt Redis (nếu chọn Redis cache)

```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# macOS
brew install redis

# Windows
# Download từ https://redis.io/download

# Start Redis
redis-server
```

### Bước 4: Cài đặt dependencies

```bash
pip install django-redis
```

### Bước 5: Monitor performance

Thêm logging để theo dõi:

```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'dashboard_performance.log',
        },
    },
    'loggers': {
        'dashboard.performance': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## Monitoring và Maintenance

### 1. Cache Hit Rate

Monitor cache hit rate để đảm bảo caching hiệu quả:

```python
from django.core.cache import cache
from django.core.cache.utils import make_template_fragment_key

# Check cache stats (Redis)
import redis
r = redis.Redis()
info = r.info()
print(f"Cache hits: {info['keyspace_hits']}")
print(f"Cache misses: {info['keyspace_misses']}")
```

### 2. Database Query Analysis

Sử dụng Django Debug Toolbar để monitor queries:

```python
# settings.py (chỉ trong development)
if DEBUG:
    INSTALLED_APPS += ['debug_toolbar']
    MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware']
```

### 3. Performance Testing

Test performance với các tools:

```bash
# Apache Bench
ab -n 100 -c 10 http://localhost:8000/api/dashboard/

# wrk
wrk -t12 -c400 -d30s http://localhost:8000/api/dashboard/
```

## Troubleshooting

### Cache không hoạt động
1. Kiểm tra Redis service: `redis-cli ping`
2. Kiểm tra Django cache config
3. Xem logs để debug

### Queries vẫn chậm
1. Kiểm tra indexes đã được tạo: `\d+ table_name` trong PostgreSQL
2. Analyze query plans: `EXPLAIN ANALYZE SELECT ...`
3. Kiểm tra database connections

### Memory issues
1. Monitor Redis memory usage
2. Adjust cache timeout nếu cần
3. Implement cache invalidation strategy

## Tối ưu hóa thêm (Optional)

### 1. Database Connection Pooling

```python
# settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'CONN_MAX_AGE': 600,  # 10 minutes
        # ... other settings
    }
}
```

### 2. Celery Cache Warming

Implement background tasks để pre-warm cache cho users thường xuyên truy cập.

### 3. API Response Compression

```python
# settings.py
MIDDLEWARE = [
    'django.middleware.gzip.GZipMiddleware',
    # ... other middleware
]
```

## Kết quả mong đợi

Sau khi áp dụng tất cả optimizations:
- ⚡ Response time giảm từ 2-5s xuống 0.2-0.8s
- 📊 Database queries giảm 70-80%
- 💾 Memory usage giảm 50%
- 🚀 Có thể handle nhiều concurrent requests hơn
- 📈 Better user experience với dashboard load nhanh hơn
