# Generated by Django for dashboard performance optimization

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '__first__'),
    ]

    operations = [
        # Index cho User.created_time để tối ưu new users queries
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_user_created_time ON cls_backend_user(created_time);",
            reverse_sql="DROP INDEX IF EXISTS idx_user_created_time;"
        ),
        
        # Composite index cho ChatMessage để tối ưu chat stats
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_chatmessage_created_type ON chatbot_chatmessage(created_at, type);",
            reverse_sql="DROP INDEX IF EXISTS idx_chatmessage_created_type;"
        ),
        
        # Composite index cho RequestLog timestamp + feature_name
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_requestlog_timestamp_feature ON request_log_requestlog(timestamp, feature_name);",
            reverse_sql="DROP INDEX IF EXISTS idx_requestlog_timestamp_feature;"
        ),
        
        # Index cho RequestLog user_id + timestamp để tối ưu active users
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_requestlog_user_timestamp ON request_log_requestlog(user_id, timestamp);",
            reverse_sql="DROP INDEX IF EXISTS idx_requestlog_user_timestamp;"
        ),
        
        # Index cho OrganizationMember organization_id
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_orgmember_org_id ON organization_organizationmember(organization_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_orgmember_org_id;"
        ),
        
        # Index cho Conversation.created_at
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_conversation_created ON chatbot_conversation(created_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_conversation_created;"
        ),
        
        # Composite index cho User organization memberships
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_user_org_created ON cls_backend_user(id) WHERE id IN (SELECT user_id FROM organization_organizationmember);",
            reverse_sql="DROP INDEX IF EXISTS idx_user_org_created;"
        ),
        
        # Index cho Conversation workspace user organization
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_conversation_workspace_user ON chatbot_conversation(workspace_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_conversation_workspace_user;"
        ),
    ]
